using System;
using System.Collections.Generic; // Added for IEnumerable in SeedData
using System.Data.Common;
using System.Linq; // Added for ToList() and other LINQ operations
using System.Threading.Tasks; // Added for Task in SeedData
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NSubstitute;
using ProScoring.Infrastructure.Authorization; // Added for AuthTypes
using ProScoring.Infrastructure.Authorization.Entities;
using ProScoring.Infrastructure.Authorization.Repositories;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.ServiceInterfaces; // For IDateTimeOffsetProvider
using ProScoring.Infrastructure.Services; // For GuidishIdGenerationUtilService and CustomIdValueGenerator
using ProScoring.Tests.Helpers; // For FixedDateTimeOffsetProvider
using Xunit;

namespace ProScoring.Tests.Authorization.Repositories
{
    /// <summary>
    /// Unit tests for the <see cref="UserAuthActionRepository"/> class.
    /// </summary>
    public class UserAuthActionRepositoryTests : IDisposable
    {
        private readonly DbConnection _connection;
        private readonly DbContextOptions<ApplicationDbContext> _dbContextOptions;
        private readonly ILogger<UserAuthActionRepository> _mockLogger;
        private ApplicationDbContext _dbContext = null!; // Keep a direct reference for seeding/setup if needed
        private UserAuthActionRepository _repository = null!;

        /// <summary>
        /// Initializes a new instance of the <see cref="UserAuthActionRepositoryTests"/> class,
        /// setting up an in-memory SQLite database and the repository for testing.
        /// </summary>
        public UserAuthActionRepositoryTests()
        {
            // Set up SQLite in-memory connection
            _connection = new SqliteConnection("Filename=:memory:");
            _connection.Open();

            _dbContextOptions = new DbContextOptionsBuilder<ApplicationDbContext>().UseSqlite(_connection).Options;

            _mockLogger = Substitute.For<ILogger<UserAuthActionRepository>>();

            // Create a new context instance for each test setup to ensure isolation for schema creation
            // and to avoid issues with shared connections if tests run in parallel (though xUnit typically doesn't for same class).
            RecreateDbContextAndRepository();
        }

        private void RecreateDbContextAndRepository()
        {
            // Dispose existing context if any, to ensure a fresh state for certain test setups if needed mid-test,
            // though typically a new context per test class instance (via constructor) is often fine with EnsureCreated().
            _dbContext?.Dispose();
            // The ApplicationDbContext constructor used here matches one of its defined signatures:
            // ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, IValueGenerator idValueGenerator, ILogger<ApplicationDbContext> logger)
            // For IValueGenerator, we pass a concrete instance of GuidishIdGenerationUtilService.
            // For AuthenticationStateProvider and IDateTimeOffsetProvider, this constructor uses defaults or nulls.
            _dbContext = new ApplicationDbContext(
                _dbContextOptions,
                null!, // For AuthenticationStateProvider
                new CustomIdValueGenerator(
                    new GuidishIdGenerationUtilService(
                        new FixedDateTimeOffsetProvider(2023, 1, 1),
                        Substitute.For<ILogger<GuidishIdGenerationUtilService>>()
                    ),
                    Substitute.For<ILogger<CustomIdValueGenerator>>()
                ),
                Substitute.For<ILogger<ApplicationDbContext>>()
            );
            _dbContext.Database.EnsureCreated(); // Ensure schema is created

            _repository = new UserAuthActionRepository(_dbContext, _mockLogger);
        }

        // Seed helper (optional, can be in each test or a common private method)
        private async Task SeedData(params UserAuthAction[] userAuthActions)
        {
            await _dbContext.UserAuthActions.AddRangeAsync(userAuthActions);
            await _dbContext.SaveChangesAsync();
            // Detach all entities to ensure fresh reads from the database in tests
            foreach (var entry in _dbContext.ChangeTracker.Entries())
            {
                entry.State = EntityState.Detached;
            }
        }

        /// <summary>
        /// Disposes of the database connection after all tests in the class have run.
        /// </summary>
        public void Dispose()
        {
            _dbContext?.Dispose();
            _connection?.Dispose();
            GC.SuppressFinalize(this);
        }

        // --- Test methods will be added below ---

        [Fact]
        public async Task AddAsync_ShouldAddUserAuthActionToDatabase()
        {
            // Arrange
            var newAction = new UserAuthAction
            {
                UserId = "user1",
                TargetId = "target1",
                AuthActionName = AuthTypes.Actions.VIEW, // Using AuthTypes
            };
            // Ensure AuthAction "View" is seeded by ApplicationDbContext or add here if needed
            // await _dbContext.AuthActions.AddAsync(new AuthAction { Name = AuthTypes.Actions.VIEW });
            // await _dbContext.SaveChangesAsync();

            // Act
            await _repository.AddAsync(newAction);
            await _dbContext.SaveChangesAsync(); // Simulate Unit of Work saving changes

            // Assert
            var retrievedAction = await _dbContext.UserAuthActions.FindAsync(
                newAction.AuthActionName,
                newAction.UserId,
                newAction.TargetId
            );
            Assert.NotNull(retrievedAction);
            Assert.Equal("user1", retrievedAction.UserId);
            Assert.Equal("target1", retrievedAction.TargetId);
            Assert.Equal(AuthTypes.Actions.VIEW, retrievedAction.AuthActionName);
        }

        [Fact]
        public async Task GetByKeyAsync_ShouldReturnCorrectUserAuthAction_WhenExists()
        {
            // Arrange
            var action1 = new UserAuthAction
            {
                UserId = "userTest1",
                TargetId = "targetTest1",
                AuthActionName = AuthTypes.Actions.VIEW,
            }; // Using AuthTypes
            var action2 = new UserAuthAction
            {
                UserId = "userTest1",
                TargetId = "targetTest2",
                AuthActionName = AuthTypes.Actions.EDIT,
            }; // Using AuthTypes
            await SeedData(action1, action2);
            // Recreate context and repo to ensure data is read from DB, not just tracked entities
            RecreateDbContextAndRepository();

            // Act
            var result = await _repository.GetByKeyAsync("userTest1", "targetTest1", AuthTypes.Actions.VIEW);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("userTest1", result.UserId);
            Assert.Equal("targetTest1", result.TargetId);
            Assert.Equal(AuthTypes.Actions.VIEW, result.AuthActionName);
        }

        [Fact]
        public async Task GetByKeyAsync_ShouldReturnNull_WhenNotExists()
        {
            // Arrange
            var action1 = new UserAuthAction
            {
                UserId = "userTest1",
                TargetId = "targetTest1",
                AuthActionName = AuthTypes.Actions.VIEW,
            }; // Using AuthTypes
            await SeedData(action1);
            // Recreate context and repo to ensure data is read from DB
            RecreateDbContextAndRepository();

            // Act
            var result = await _repository.GetByKeyAsync("userTest1", "targetTest1", "Action.NonExistent");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetByKeyAsync_ShouldReturnNull_WhenKeyPartsMismatch()
        {
            // Arrange
            var action1 = new UserAuthAction
            {
                UserId = "userTest1",
                TargetId = "targetTest1",
                AuthActionName = AuthTypes.Actions.VIEW,
            }; // Using AuthTypes
            await SeedData(action1);
            // Recreate context and repo
            RecreateDbContextAndRepository();

            // Act
            var resultUser = await _repository.GetByKeyAsync("userTest-Wrong", "targetTest1", AuthTypes.Actions.VIEW);
            var resultTarget = await _repository.GetByKeyAsync("userTest1", "targetTest-Wrong", AuthTypes.Actions.VIEW);
            var resultAction = await _repository.GetByKeyAsync("userTest1", "targetTest1", "Action.Wrong");

            // Assert
            Assert.Null(resultUser);
            Assert.Null(resultTarget);
            Assert.Null(resultAction);
        }

        // PlaceholderTest_ToRemove can now be removed or commented out.
        // [Fact]
        // public void PlaceholderTest_ToRemove()
        // {
        //     Assert.True(true);
        // }

        [Fact]
        public async Task GetByUserAsync_ShouldReturnOnlyUserAuthActionsForGivenUser()
        {
            // Arrange
            await SeedData(
                new UserAuthAction
                {
                    UserId = "user1",
                    TargetId = "target1",
                    AuthActionName = AuthTypes.Actions.VIEW,
                },
                new UserAuthAction
                {
                    UserId = "user1",
                    TargetId = "target2",
                    AuthActionName = AuthTypes.Actions.EDIT,
                },
                new UserAuthAction
                {
                    UserId = "user2",
                    TargetId = "target1",
                    AuthActionName = AuthTypes.Actions.VIEW,
                }
            );
            RecreateDbContextAndRepository();

            // Act
            var result = (await _repository.GetByUserAsync("user1")).ToList();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.All(result, item => Assert.Equal("user1", item.UserId));
            Assert.Contains(
                result,
                item => item.TargetId == "target1" && item.AuthActionName == AuthTypes.Actions.VIEW
            );
            Assert.Contains(
                result,
                item => item.TargetId == "target2" && item.AuthActionName == AuthTypes.Actions.EDIT
            );
        }

        [Fact]
        public async Task GetByUserAsync_ShouldReturnEmptyList_WhenUserHasNoActions()
        {
            // Arrange
            await SeedData(
                new UserAuthAction
                {
                    UserId = "user2",
                    TargetId = "target1",
                    AuthActionName = AuthTypes.Actions.VIEW,
                }
            );
            RecreateDbContextAndRepository();

            // Act
            var result = (await _repository.GetByUserAsync("user1")).ToList(); // user1 has no actions

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetByUserAndTargetAsync_ShouldReturnOnlyMatchingActions()
        {
            // Arrange
            await SeedData(
                new UserAuthAction
                {
                    UserId = "user1",
                    TargetId = "target1",
                    AuthActionName = AuthTypes.Actions.VIEW,
                },
                new UserAuthAction
                {
                    UserId = "user1",
                    TargetId = "target1",
                    AuthActionName = AuthTypes.Actions.EDIT,
                },
                new UserAuthAction
                {
                    UserId = "user1",
                    TargetId = "target2",
                    AuthActionName = AuthTypes.Actions.VIEW,
                },
                new UserAuthAction
                {
                    UserId = "user2",
                    TargetId = "target1",
                    AuthActionName = AuthTypes.Actions.VIEW,
                }
            );
            RecreateDbContextAndRepository();

            // Act
            var result = (await _repository.GetByUserAndTargetAsync("user1", "target1")).ToList();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.All(
                result,
                item =>
                {
                    Assert.Equal("user1", item.UserId);
                    Assert.Equal("target1", item.TargetId);
                }
            );
            Assert.Contains(result, item => item.AuthActionName == AuthTypes.Actions.VIEW);
            Assert.Contains(result, item => item.AuthActionName == AuthTypes.Actions.EDIT);
        }

        [Fact]
        public async Task AddRangeAsync_ShouldAddAllUserAuthActionsToDatabase()
        {
            // Arrange
            var actionsToAdd = new List<UserAuthAction>
            {
                new UserAuthAction
                {
                    UserId = "userMulti1",
                    TargetId = "targetMulti1",
                    AuthActionName = AuthTypes.Actions.VIEW,
                },
                new UserAuthAction
                {
                    UserId = "userMulti1",
                    TargetId = "targetMulti2",
                    AuthActionName = AuthTypes.Actions.EDIT,
                },
            };

            // Act
            await _repository.AddRangeAsync(actionsToAdd);
            await _dbContext.SaveChangesAsync(); // Simulate Unit of Work

            // Assert
            var count = await _dbContext.UserAuthActions.CountAsync(uaa => uaa.UserId == "userMulti1");
            Assert.Equal(2, count);
            Assert.NotNull(
                await _dbContext.UserAuthActions.FindAsync(AuthTypes.Actions.VIEW, "userMulti1", "targetMulti1")
            );
            Assert.NotNull(
                await _dbContext.UserAuthActions.FindAsync(AuthTypes.Actions.EDIT, "userMulti1", "targetMulti2")
            );
        }

        [Fact]
        public async Task DeleteByKeyAsync_ShouldRemoveUserAuthActionAndReturnTrue_WhenExists()
        {
            // Arrange
            var actionToDelete = new UserAuthAction
            {
                UserId = "userDel1",
                TargetId = "targetDel1",
                AuthActionName = AuthTypes.Actions.DELETE,
            };
            await SeedData(actionToDelete);
            RecreateDbContextAndRepository(); // Ensure it's read for deletion from a clean context state

            // Act
            var result = await _repository.DeleteByKeyAsync(
                actionToDelete.UserId,
                actionToDelete.TargetId,
                actionToDelete.AuthActionName
            );
            await _dbContext.SaveChangesAsync(); // Simulate Unit of Work

            // Assert
            Assert.True(result);
            var deleted = await _repository.GetByKeyAsync(
                actionToDelete.UserId,
                actionToDelete.TargetId,
                actionToDelete.AuthActionName
            );
            Assert.Null(deleted);
        }

        [Fact]
        public async Task DeleteByKeyAsync_ShouldReturnFalse_WhenNotExists()
        {
            // Arrange
            // No data seeded for this key
            RecreateDbContextAndRepository();

            // Act
            var result = await _repository.DeleteByKeyAsync("userNonExist", "targetNonExist", "ActionNonExist");
            // No SaveChangesAsync needed as nothing should have changed

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task DeleteRangeAsync_ShouldRemoveSpecifiedUserAuthActions()
        {
            // Arrange
            var action1 = new UserAuthAction
            {
                UserId = "userRangeDel1",
                TargetId = "targetRange1",
                AuthActionName = AuthTypes.Actions.VIEW,
            };
            var action2 = new UserAuthAction
            {
                UserId = "userRangeDel1",
                TargetId = "targetRange2",
                AuthActionName = AuthTypes.Actions.EDIT,
            };
            var actionToKeep = new UserAuthAction
            {
                UserId = "userRangeDel2",
                TargetId = "targetRange1",
                AuthActionName = AuthTypes.Actions.VIEW,
            };
            await SeedData(action1, action2, actionToKeep);

            // For DeleteRangeAsync, entities typically need to be tracked by the DbContext instance that RemoveRange is called on.
            // So, we re-fetch them using the same DbContext instance as the repository will use for deletion.
            RecreateDbContextAndRepository();
            var trackedAction1 = await _dbContext.UserAuthActions.FindAsync(
                action1.AuthActionName,
                action1.UserId,
                action1.TargetId
            );
            var trackedAction2 = await _dbContext.UserAuthActions.FindAsync(
                action2.AuthActionName,
                action2.UserId,
                action2.TargetId
            );
            Assert.NotNull(trackedAction1); // Ensure they were found before deletion
            Assert.NotNull(trackedAction2);

            var actionsToDelete = new List<UserAuthAction> { trackedAction1, trackedAction2 };

            // Act
            await _repository.DeleteRangeAsync(actionsToDelete); // This is synchronous in EF Core for RemoveRange
            await _dbContext.SaveChangesAsync(); // Simulate Unit of Work

            // Assert
            Assert.Null(await _repository.GetByKeyAsync(action1.UserId, action1.TargetId, action1.AuthActionName));
            Assert.Null(await _repository.GetByKeyAsync(action2.UserId, action2.TargetId, action2.AuthActionName));
            Assert.NotNull(
                await _repository.GetByKeyAsync(actionToKeep.UserId, actionToKeep.TargetId, actionToKeep.AuthActionName)
            );
        }

        [Fact]
        public async Task DeleteRangeAsync_ShouldHandleEmptyListGracefully()
        {
            // Arrange
            var emptyList = new List<UserAuthAction>();
            RecreateDbContextAndRepository();

            // Act
            await _repository.DeleteRangeAsync(emptyList);
            await _dbContext.SaveChangesAsync();

            // Assert
            // No exception should be thrown, and the database should remain unchanged.
            // (Add an assertion here if you have a way to count total items, etc.)
            Assert.True(true); // Placeholder for successful execution
        }
    }
}
